<?php

/**
 * Template name: Join Now
 */

// Start session to handle messages
if (session_status() == PHP_SESSION_NONE) {
  session_start();
}

// Get form errors, data, and messages from session
$form_errors = isset($_SESSION['form_errors']) ? $_SESSION['form_errors'] : array();
$form_data = isset($_SESSION['form_data']) ? $_SESSION['form_data'] : array();
$form_error = isset($_SESSION['form_error']) ? $_SESSION['form_error'] : '';
$form_success = isset($_SESSION['form_success']) ? $_SESSION['form_success'] : '';

// Clear messages after displaying them
if (!empty($form_errors)) {
  unset($_SESSION['form_errors']);
}
if (!empty($form_data)) {
  unset($_SESSION['form_data']);
}
if (!empty($form_error)) {
  unset($_SESSION['form_error']);
}
if (!empty($form_success)) {
  unset($_SESSION['form_success']);
}

get_header(); ?>

<style>
  .alert {
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px;
  }

  .alert-success {
    color: #3c763d;
    background-color: #dff0d8;
    border-color: #d6e9c6;
  }

  .alert-danger {
    color: #a94442;
    background-color: #f2dede;
    border-color: #ebccd1;
  }

  .form-error {
    color: #a94442;
    font-size: 12px;
    margin-top: 5px;
  }

  .has-error input,
  .has-error select,
  .has-error textarea {
    border-color: #a94442;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
  }

  .has-error input:focus,
  .has-error select:focus,
  .has-error textarea:focus {
    border-color: #843534;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 6px #ce8483;
  }

  /* Real-time validation styles */
  .field-error {
    color: #dc3545;
    font-size: 12px;
    margin-top: 5px;
    display: none;
    font-weight: 500;
  }

  .field-error.show {
    display: block;
  }

  .field-valid {
    border-color: #28a745 !important;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 6px rgba(40, 167, 69, 0.3) !important;
  }

  .field-invalid {
    border-color: #dc3545 !important;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 6px rgba(220, 53, 69, 0.3) !important;
  }

  .form-group {
    position: relative;
    margin-bottom: 20px;
  }

  /* Enhanced error visibility */
  .alert-danger {
    animation: fadeInShake 0.6s ease-in-out;
    position: relative;
    z-index: 1000;
  }

  @keyframes fadeInShake {
    0% {
      opacity: 0;
      transform: translateX(-10px);
    }

    50% {
      opacity: 0.8;
      transform: translateX(5px);
    }

    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }

  /* Ensure error messages are always visible */
  .field-error.show {
    display: block !important;
    animation: slideDown 0.3s ease-out;
  }

  @keyframes slideDown {
    from {
      opacity: 0;
      max-height: 0;
    }

    to {
      opacity: 1;
      max-height: 50px;
    }
  }
</style>

<section class="pagetitle">
  <div class="container">
    <h1>Membership Registration Form</h1>
  </div>
</section>
<section class="section-membership">
  <div class="container">

    <?php if (!empty($form_success)): ?>
      <div class="alert alert-success">
        <strong>Success!</strong> <?php echo esc_html($form_success); ?>
      </div>
    <?php endif; ?>

    <?php if (!empty($form_error)): ?>
      <div class="alert alert-danger">
        <strong>Error!</strong> <?php echo esc_html($form_error); ?>
      </div>
    <?php endif; ?>

    <?php if (!empty($form_errors)): ?>
      <div class="alert alert-danger">
        <strong>Please correct the following errors:</strong>
        <ul style="margin-top: 10px; margin-bottom: 0;">
          <?php foreach ($form_errors as $field => $error): ?>
            <li><?php echo esc_html($error); ?></li>
          <?php endforeach; ?>
        </ul>
      </div>
    <?php endif; ?>

    <!-- Custom Membership Registration Form -->
    <form id="membership-form" method="post" enctype="multipart/form-data" action="<?php echo esc_url(admin_url('admin-post.php')); ?>">
      <?php wp_nonce_field('membership_form_nonce', 'membership_nonce'); ?>
      <input type="hidden" name="action" value="handle_membership_form">
      <input type="hidden" id="ajax-url" value="<?php echo admin_url('admin-ajax.php'); ?>">
      <?php
      // Add a PHP variable to indicate if reCAPTCHA keys are set
      $recaptcha_site_key = get_option('gchk_recaptcha_site_key', '');
      $recaptcha_secret_key = get_option('gchk_recaptcha_secret_key', '');
      $show_recaptcha = !empty($recaptcha_site_key) && !empty($recaptcha_secret_key);
      ?>

      <h3><strong>Section 1 : PERSONAL INFORMATION</strong></h3>
      <div class="row">
        <div class="col-md-5 form-group <?php echo isset($form_errors['full-name']) ? 'has-error' : ''; ?>">
          <p>Full Name :</p>
          <input type="text" name="full-name" class="form-control" value="<?php echo isset($form_data['full_name']) ? esc_attr($form_data['full_name']) : ''; ?>">
          <?php if (isset($form_errors['full-name'])): ?>
            <div class="form-error"><?php echo esc_html($form_errors['full-name']); ?></div>
          <?php endif; ?>
          <div class="field-error" id="full-name-error"></div>
        </div>
        <div class="col-md-5 form-group <?php echo isset($form_errors['birth-date']) ? 'has-error' : ''; ?>">
          <p>Date of Birth : </p>
          <input type="date" name="birth-date" class="form-control" value="<?php echo isset($form_data['birth_date']) ? esc_attr($form_data['birth_date']) : ''; ?>">
          <?php if (isset($form_errors['birth-date'])): ?>
            <div class="form-error"><?php echo esc_html($form_errors['birth-date']); ?></div>
          <?php endif; ?>
          <div class="field-error" id="birth-date-error"></div>
        </div>
        <div class="col-md-2 form-group <?php echo isset($form_errors['gender']) ? 'has-error' : ''; ?>">
          <p>Gender : </p>
          <div class="radio-check">
            <label><input type="radio" name="gender" value="M" <?php echo (isset($form_data['gender']) && $form_data['gender'] == 'M') ? 'checked' : ''; ?>> M</label>
            <label><input type="radio" name="gender" value="F" <?php echo (isset($form_data['gender']) && $form_data['gender'] == 'F') ? 'checked' : ''; ?>> F</label>
          </div>
          <?php if (isset($form_errors['gender'])): ?>
            <div class="form-error"><?php echo esc_html($form_errors['gender']); ?></div>
          <?php endif; ?>
          <div class="field-error" id="gender-error"></div>
        </div>
      </div>

      <h3><strong>Section 2 : CONTACT INFORMATION</strong></h3>
      <div class="row">
        <div class="col-md-6 form-group <?php echo isset($form_errors['phone-number']) ? 'has-error' : ''; ?>">
          <p>Phone Number : </p>
          <input type="tel" name="phone-number" class="form-control" value="<?php echo isset($form_data['phone_number']) ? esc_attr($form_data['phone_number']) : ''; ?>">
          <?php if (isset($form_errors['phone-number'])): ?>
            <div class="form-error"><?php echo esc_html($form_errors['phone-number']); ?></div>
          <?php endif; ?>
          <div class="field-error" id="phone-number-error"></div>
        </div>
        <div class="col-md-6 form-group <?php echo isset($form_errors['email-address']) ? 'has-error' : ''; ?>">
          <p>Email Address : </p>
          <input type="email" name="email-address" class="form-control" value="<?php echo isset($form_data['email_address']) ? esc_attr($form_data['email_address']) : ''; ?>">
          <?php if (isset($form_errors['email-address'])): ?>
            <div class="form-error"><?php echo esc_html($form_errors['email-address']); ?></div>
          <?php endif; ?>
          <div class="field-error" id="email-address-error"></div>
        </div>
        <div class="col-md-6 form-group <?php echo isset($form_errors['address']) ? 'has-error' : ''; ?>">
          <p>Address : </p>
          <input type="text" name="address" class="form-control" value="<?php echo isset($form_data['address']) ? esc_attr($form_data['address']) : ''; ?>">
          <small>(Street, City, State/Province, Country, Postal Code)</small>
          <?php if (isset($form_errors['address'])): ?>
            <div class="form-error"><?php echo esc_html($form_errors['address']); ?></div>
          <?php endif; ?>
          <div class="field-error" id="address-error"></div>
        </div>
        <div class="col-md-6 form-group <?php echo isset($form_errors['country-residence']) ? 'has-error' : ''; ?>">
          <p>Country of Residence :</p>
          <input type="text" name="country-residence" class="form-control" value="<?php echo isset($form_data['country_residence']) ? esc_attr($form_data['country_residence']) : ''; ?>">
          <?php if (isset($form_errors['country-residence'])): ?>
            <div class="form-error"><?php echo esc_html($form_errors['country-residence']); ?></div>
          <?php endif; ?>
          <div class="field-error" id="country-residence-error"></div>
        </div>
      </div>

      <h3><strong>Section 3 : GOVERNMENT IDENTIFICATION</strong></h3>
      <div class="row">
        <div class="col-md-4 form-group <?php echo isset($form_errors['identification-number']) ? 'has-error' : ''; ?>">
          <p>Identification Number : </p>
          <input type="text" name="identification-number" class="form-control" value="<?php echo isset($form_data['identification_number']) ? esc_attr($form_data['identification_number']) : ''; ?>">
          <?php if (isset($form_errors['identification-number'])): ?>
            <div class="form-error"><?php echo esc_html($form_errors['identification-number']); ?></div>
          <?php endif; ?>
          <div class="field-error" id="identification-number-error"></div>
        </div>
        <div class="col-md-4 form-group <?php echo isset($form_errors['issuing-country']) ? 'has-error' : ''; ?>">
          <p>Issuing Country :</p>
          <input type="text" name="issuing-country" class="form-control" value="<?php echo isset($form_data['issuing_country']) ? esc_attr($form_data['issuing_country']) : ''; ?>">
          <?php if (isset($form_errors['issuing-country'])): ?>
            <div class="form-error"><?php echo esc_html($form_errors['issuing-country']); ?></div>
          <?php endif; ?>
          <div class="field-error" id="issuing-country-error"></div>
        </div>
        <div class="col-md-4 form-group <?php echo isset($form_errors['expiration-date']) ? 'has-error' : ''; ?>">
          <p>Expiration Date : </p>
          <input type="date" name="expiration-date" class="form-control" value="<?php echo isset($form_data['expiration_date']) ? esc_attr($form_data['expiration_date']) : ''; ?>" min="2024-12-07">
          <?php if (isset($form_errors['expiration-date'])): ?>
            <div class="form-error"><?php echo esc_html($form_errors['expiration-date']); ?></div>
          <?php endif; ?>
          <div class="field-error" id="expiration-date-error"></div>
        </div>
        <div class="col-md-6 form-group <?php echo isset($form_errors['id-number']) ? 'has-error' : ''; ?>">
          <p>National ID Number : </p>
          <input type="text" name="id-number" class="form-control" value="<?php echo isset($form_data['id_number']) ? esc_attr($form_data['id_number']) : ''; ?>">
          <?php if (isset($form_errors['id-number'])): ?>
            <div class="form-error"><?php echo esc_html($form_errors['id-number']); ?></div>
          <?php endif; ?>
          <div class="field-error" id="id-number-error"></div>
        </div>
        <div class="col-md-6 form-group <?php echo isset($form_errors['passport-number']) ? 'has-error' : ''; ?>">
          <p>Passport Number :</p>
          <input type="text" name="passport-number" class="form-control" value="<?php echo isset($form_data['passport_number']) ? esc_attr($form_data['passport_number']) : ''; ?>">
          <?php if (isset($form_errors['passport-number'])): ?>
            <div class="form-error"><?php echo esc_html($form_errors['passport-number']); ?></div>
          <?php endif; ?>
          <div class="field-error" id="passport-number-error"></div>
        </div>
        <div class="col-md-6 form-group <?php echo isset($form_errors['place-issue']) ? 'has-error' : ''; ?>">
          <p>Place of issue : </p>
          <input type="text" name="place-issue" class="form-control" value="<?php echo isset($form_data['place_issue']) ? esc_attr($form_data['place_issue']) : ''; ?>">
          <?php if (isset($form_errors['place-issue'])): ?>
            <div class="form-error"><?php echo esc_html($form_errors['place-issue']); ?></div>
          <?php endif; ?>
          <div class="field-error" id="place-issue-error"></div>
        </div>
        <div class="col-md-6 form-group <?php echo isset($form_errors['date-issue']) ? 'has-error' : ''; ?>">
          <p>Date of issue : </p>
          <input type="date" name="date-issue" class="form-control" value="<?php echo isset($form_data['date_issue']) ? esc_attr($form_data['date_issue']) : ''; ?>">
          <?php if (isset($form_errors['date-issue'])): ?>
            <div class="form-error"><?php echo esc_html($form_errors['date-issue']); ?></div>
          <?php endif; ?>
          <div class="field-error" id="date-issue-error"></div>
        </div>
      </div>

      <h3><strong>Section 4 : EMERGENCY CONTACT INFORMATION</strong></h3>
      <div class="row">
        <div class="col-md-6 form-group <?php echo isset($form_errors['contact-name']) ? 'has-error' : ''; ?>">
          <p>Contact Name :</p>
          <input type="text" name="contact-name" class="form-control" value="<?php echo isset($form_data['contact_name']) ? esc_attr($form_data['contact_name']) : ''; ?>">
          <?php if (isset($form_errors['contact-name'])): ?>
            <div class="form-error"><?php echo esc_html($form_errors['contact-name']); ?></div>
          <?php endif; ?>
          <div class="field-error" id="contact-name-error"></div>
        </div>
        <div class="col-md-6 form-group <?php echo isset($form_errors['contact-relationship']) ? 'has-error' : ''; ?>">
          <p>Contact Relationship :</p>
          <input type="text" name="contact-relationship" class="form-control" value="<?php echo isset($form_data['contact_relationship']) ? esc_attr($form_data['contact_relationship']) : ''; ?>">
          <?php if (isset($form_errors['contact-relationship'])): ?>
            <div class="form-error"><?php echo esc_html($form_errors['contact-relationship']); ?></div>
          <?php endif; ?>
          <div class="field-error" id="contact-relationship-error"></div>
        </div>
        <div class="col-md-6 form-group <?php echo isset($form_errors['contact-number']) ? 'has-error' : ''; ?>">
          <p>Contact Phone Number :</p>
          <input type="tel" name="contact-number" class="form-control" value="<?php echo isset($form_data['contact_number']) ? esc_attr($form_data['contact_number']) : ''; ?>">
          <?php if (isset($form_errors['contact-number'])): ?>
            <div class="form-error"><?php echo esc_html($form_errors['contact-number']); ?></div>
          <?php endif; ?>
          <div class="field-error" id="contact-number-error"></div>
        </div>
        <div class="col-md-6 form-group <?php echo isset($form_errors['contact-email']) ? 'has-error' : ''; ?>">
          <p>Contact Email Address :</p>
          <input type="email" name="contact-email" class="form-control" value="<?php echo isset($form_data['contact_email']) ? esc_attr($form_data['contact_email']) : ''; ?>">
          <?php if (isset($form_errors['contact-email'])): ?>
            <div class="form-error"><?php echo esc_html($form_errors['contact-email']); ?></div>
          <?php endif; ?>
          <div class="field-error" id="contact-email-error"></div>
        </div>
      </div>

      <h3><strong>Section 5 : PROFESSIONAL INFORMATION</strong></h3>
      <div class="row">
        <div class="col-md-4 form-group <?php echo isset($form_errors['occupation']) ? 'has-error' : ''; ?>">
          <p>Occupation :</p>
          <input type="text" name="occupation" class="form-control" value="<?php echo isset($form_data['occupation']) ? esc_attr($form_data['occupation']) : ''; ?>">
          <?php if (isset($form_errors['occupation'])): ?>
            <div class="form-error"><?php echo esc_html($form_errors['occupation']); ?></div>
          <?php endif; ?>
          <div class="field-error" id="occupation-error"></div>
        </div>
        <div class="col-md-4 form-group <?php echo isset($form_errors['employer']) ? 'has-error' : ''; ?>">
          <p>Employer :</p>
          <input type="text" name="employer" class="form-control" value="<?php echo isset($form_data['employer']) ? esc_attr($form_data['employer']) : ''; ?>">
          <?php if (isset($form_errors['employer'])): ?>
            <div class="form-error"><?php echo esc_html($form_errors['employer']); ?></div>
          <?php endif; ?>
          <div class="field-error" id="employer-error"></div>
        </div>
        <div class="col-md-4 form-group <?php echo isset($form_errors['professional-aff']) ? 'has-error' : ''; ?>">
          <p>Professional Affiliations:</p>
          <input type="text" name="professional-aff" class="form-control" value="<?php echo isset($form_data['professional_aff']) ? esc_attr($form_data['professional_aff']) : ''; ?>">
          <?php if (isset($form_errors['professional-aff'])): ?>
            <div class="form-error"><?php echo esc_html($form_errors['professional-aff']); ?></div>
          <?php endif; ?>
          <div class="field-error" id="professional-aff-error"></div>
        </div>
      </div>

      <div class="row">
        <div class="col-md-6 form-group <?php echo isset($form_errors['photo-file']) ? 'has-error' : ''; ?>">
          <h3><strong>Section 6 : RECENT PHOTOGRAPH</strong></h3>
          <p>Attach your Passport-sized Photo</p>
          <input type="file" name="photo-file" accept="image/*">
          <small>Maximum file size: 2MB</small>
          <?php if (isset($form_errors['photo-file'])): ?>
            <div class="form-error"><?php echo esc_html($form_errors['photo-file']); ?></div>
          <?php endif; ?>
          <div class="field-error" id="photo-file-error"></div>
        </div>
        <div class="col-md-6 form-group <?php echo isset($form_errors['signature-file']) ? 'has-error' : ''; ?>">
          <h3><strong>Section 7 : CONSENT & SIGNATURE</strong></h3>
          <div class="form-group <?php echo isset($form_errors['consent']) ? 'has-error' : ''; ?>">
            <label>
              <input type="checkbox" name="consent" value="1" <?php echo (isset($form_data['consent']) && $form_data['consent']) ? 'checked' : ''; ?>>
              I agree to share my information with the consulate/embassy for membership purposes.
            </label>
            <?php if (isset($form_errors['consent'])): ?>
              <div class="form-error"><?php echo esc_html($form_errors['consent']); ?></div>
            <?php endif; ?>
            <div class="field-error" id="consent-error"></div>
          </div>
          <p>Attach your Digital Signature:</p>
          <input type="file" name="signature-file" accept="image/*">
          <small>Maximum file size: 1MB</small>
          <?php if (isset($form_errors['signature-file'])): ?>
            <div class="form-error"><?php echo esc_html($form_errors['signature-file']); ?></div>
          <?php endif; ?>
          <div class="field-error" id="signature-file-error"></div>
        </div>
      </div>

      <div class="row">
        <div class="col-md-6">
          <?php if ($show_recaptcha): ?>
            <div class="form-group" id="recaptcha-container">
              <div class="g-recaptcha" data-sitekey="<?php echo esc_attr($recaptcha_site_key); ?>"></div>
              <div class="field-error" id="recaptcha-error"></div>
            </div>
          <?php endif; ?>
          <input type="submit" value="Submit" class="btn btn-primary">
        </div>
      </div>
    </form>

    <article class="join-article">
      <?php echo get_the_post_thumbnail(get_the_ID(), 'full'); ?>
    </article>
  </div>
</section>

<div id="form-toast" style="display:none;position:fixed;top:30px;left:50%;transform:translateX(-50%);z-index:9999;min-width:320px;max-width:90vw;"></div>

<script src="https://www.google.com/recaptcha/api.js" async defer></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('membership-form');
    const ajaxUrl = document.getElementById('ajax-url').value;
    const showRecaptcha = <?php echo $show_recaptcha ? 'true' : 'false'; ?>;

    // Helper regex
    const regex = {
      name: /^[A-Za-z\s]{2,}$/,
      country: /^[A-Za-z\s]+$/,
      min2: /^.{2,}$/,
      min6: /^.{6,}$/,
      email: /^[^@\s]+@[^@\s]+\.[^@\s]+$/,
      phone: /^[0-9\-\+\s\(\)]{7,}$/,
      date: /^\d{4}-\d{2}-\d{2}$/
    };

    // Validation rules for all fields
    const validationRules = {
      'full-name': {
        required: true,
        regex: regex.name,
        message: 'Full Name is required, letters and spaces only, min 2 characters.'
      },
      'birth-date': {
        required: true,
        message: 'Date of Birth is required.'
      },
      'gender': {
        required: true,
        message: 'Gender is required.'
      },
      'phone-number': {
        required: true,
        regex: regex.phone,
        message: 'Phone Number is required and must be valid.'
      },
      'email-address': {
        required: true,
        regex: regex.email,
        message: 'Valid Email Address is required.'
      },
      'address': {
        required: true,
        message: 'Address is required.'
      },
      'country-residence': {
        required: true,
        regex: regex.country,
        message: 'Country of Residence is required, letters and spaces only.'
      },
      'identification-number': {
        required: true,
        message: 'Identification Number is required.'
      },
      'issuing-country': {
        required: true,
        regex: regex.country,
        message: 'Issuing Country is required, letters and spaces only.'
      },
      'expiration-date': {
        required: true,
        future: true,
        message: 'Expiration Date is required and must be a future date.'
      },
      'id-number': {
        required: true,
        message: 'National ID Number is required.'
      },
      'passport-number': {
        required: true,
        regex: regex.min6,
        message: 'Passport Number is required, min 6 characters.'
      },
      'place-issue': {
        required: true,
        regex: regex.country,
        message: 'Place of Issue is required, letters and spaces only.'
      },
      'date-issue': {
        required: true,
        notFuture: true,
        message: 'Date of Issue is required and cannot be a future date.'
      },
      'contact-name': {
        required: true,
        regex: regex.name,
        message: 'Emergency Contact Name is required, letters and spaces only.'
      },
      'contact-relationship': {
        required: true,
        regex: regex.min2,
        message: 'Emergency Contact Relationship is required, min 2 characters.'
      },
      'contact-number': {
        required: true,
        regex: regex.phone,
        message: 'Emergency Contact Phone Number is required.'
      },
      'contact-email': {
        required: true,
        regex: regex.email,
        message: 'Emergency Contact Email Address is required and must be valid.'
      },
      'occupation': {
        required: true,
        regex: regex.min2,
        message: 'Occupation is required, min 2 characters.'
      },
      'employer': {
        required: true,
        regex: regex.min2,
        message: 'Employer is required, min 2 characters.'
      },
      'professional-aff': {
        required: true,
        regex: regex.min2,
        message: 'Professional Affiliations is required, min 2 characters.'
      },
      'photo-file': {
        required: true,
        file: true,
        maxSize: 2 * 1024 * 1024,
        message: 'Passport Photo is required, must be an image file, max 2MB.'
      },
      'signature-file': {
        required: true,
        file: true,
        maxSize: 1 * 1024 * 1024,
        message: 'Digital Signature is required, must be an image file, max 1MB.'
      },
      'consent': {
        required: true,
        checkbox: true,
        message: 'Consent is required.'
      },
      'g-recaptcha-response': {
        required: showRecaptcha,
        message: 'Please complete the reCAPTCHA.'
      }
    };

    // Helper: show/hide error
    function showError(fieldName, message) {
      const errorElement = document.getElementById(fieldName + '-error');
      const input = document.querySelector(`[name="${fieldName}"]`);
      if (errorElement) {
        errorElement.textContent = message;
        errorElement.classList.add('show');
      }
      if (input) {
        input.classList.remove('field-valid');
        input.classList.add('field-invalid');
      }
    }
    function hideError(fieldName) {
      const errorElement = document.getElementById(fieldName + '-error');
      const input = document.querySelector(`[name="${fieldName}"]`);
      if (errorElement) errorElement.classList.remove('show');
      if (input) {
        input.classList.remove('field-invalid');
        input.classList.add('field-valid');
      }
    }

    // Validate a single field
    function validateField(fieldName) {
      const rule = validationRules[fieldName];
      if (!rule) return true;
      let input = document.querySelector(`[name="${fieldName}"]`);
      let value = input ? (input.type === 'checkbox' ? input.checked : input.value) : '';
      let valid = true;
      let msg = rule.message;
      if (rule.required && (!value || (input && input.type === 'checkbox' && !input.checked))) valid = false;
      if (valid && rule.regex && value && !rule.regex.test(value)) valid = false;
      if (valid && rule.file && input && input.files.length > 0) {
        const file = input.files[0];
        if (!file.type.startsWith('image/')) valid = false;
        if (file.size > rule.maxSize) valid = false;
      }
      if (valid && rule.future && value) {
        const today = new Date();
        const valDate = new Date(value);
        if (valDate <= today) valid = false;
      }
      if (valid && rule.notFuture && value) {
        const today = new Date();
        const valDate = new Date(value);
        if (valDate > today) valid = false;
      }
      if (!valid) showError(fieldName, msg);
      else hideError(fieldName);
      return valid;
    }

    // Validate all fields
    function validateAllFields() {
      let isValid = true;
      Object.keys(validationRules).forEach(fieldName => {
        if (!validateField(fieldName)) isValid = false;
      });
      return isValid;
    }

    // Real-time validation
    Object.keys(validationRules).forEach(fieldName => {
      const input = document.querySelector(`[name="${fieldName}"]`);
      if (!input) return;
      if (input.type === 'file' || input.type === 'checkbox') {
        input.addEventListener('change', () => validateField(fieldName));
      } else {
        input.addEventListener('input', () => validateField(fieldName));
      }
    });

    // Gender radio buttons
    document.querySelectorAll('input[name="gender"]').forEach(radio => {
      radio.addEventListener('change', () => validateField('gender'));
    });

    // Add toast function
    function showToast(message, type = 'success') {
      const toast = document.getElementById('form-toast');
      toast.innerHTML = `<div class="alert alert-${type}" style="margin:0 auto;box-shadow:0 2px 8px rgba(0,0,0,0.08);font-size:16px;display:flex;align-items:center;justify-content:space-between;"><span>${message}</span><button style='background:none;border:none;font-size:20px;line-height:1;cursor:pointer;margin-left:20px;' onclick='this.closest("#form-toast").style.display="none";'>&times;</button></div>`;
      toast.style.display = 'block';
      setTimeout(() => { toast.style.display = 'none'; }, 5000);
    }

    // AJAX form submission
    form.addEventListener('submit', function(e) {
      e.preventDefault();
      let isValid = validateAllFields();
      if (!isValid) return;
      const formData = new FormData(form);
      // Add AJAX flag
      formData.append('ajax', '1');
      // Show loading state
      const submitBtn = form.querySelector('input[type="submit"]');
      submitBtn.value = 'Submitting...';
      submitBtn.disabled = true;
      // Remove previous errors
      document.querySelectorAll('.field-error').forEach(el => el.classList.remove('show'));
      // AJAX request
      fetch(ajaxUrl, {
        method: 'POST',
        body: formData
      })
      .then(res => res.json())
      .then(data => {
        submitBtn.value = 'Submit';
        submitBtn.disabled = false;
        if (data.success) {
          form.reset();
          document.querySelectorAll('.field-valid').forEach(el => el.classList.remove('field-valid'));
          showToast('Your application has been submitted successfully! Please check your email for login instructions.', 'success');
          if (showRecaptcha && window.grecaptcha) grecaptcha.reset();
        } else if (data.errors) {
          Object.keys(data.errors).forEach(field => {
            showError(field, data.errors[field]);
          });
          // Scroll to first error
          const firstError = document.querySelector('.field-error.show');
          if (firstError) firstError.scrollIntoView({behavior:'smooth', block:'center'});
        } else {
          showToast('An unexpected error occurred. Please try again.', 'danger');
        }
      })
      .catch(() => {
        submitBtn.value = 'Submit';
        submitBtn.disabled = false;
        showToast('An error occurred. Please try again.', 'danger');
      });
    });
  });
</script>

<?php get_footer(); ?>